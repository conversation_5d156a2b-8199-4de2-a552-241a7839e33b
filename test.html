<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>随机选择外卖员</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .display-area {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 40px 20px;
            margin: 30px 0;
            min-height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3px solid #e9ecef;
            position: relative;
            overflow: hidden;
        }

        .current-name {
            font-size: 2.5em;
            font-weight: bold;
            color: #495057;
            transition: all 0.3s ease;
        }

        .current-name.spinning {
            animation: spin 0.1s linear infinite;
            color: #007bff;
        }

        .current-name.selected {
            animation: bounce 0.6s ease-in-out;
            color: #dc3545;
            font-size: 3em;
        }

        @keyframes spin {
            0% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.1) rotate(180deg); }
            100% { transform: scale(1) rotate(360deg); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-20px);
            }
            60% {
                transform: translateY(-10px);
            }
        }

        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 1.2em;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(238, 90, 36, 0.3);
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(238, 90, 36, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .people-list {
            margin: 20px 0;
            text-align: left;
        }

        .people-list h3 {
            color: #495057;
            margin-bottom: 15px;
            text-align: center;
        }

        .person-item {
            background: #e9ecef;
            padding: 10px 15px;
            margin: 5px 0;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .person-item.selected-person {
            background: #dc3545;
            color: white;
            animation: highlight 1s ease-in-out;
        }

        @keyframes highlight {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .result-area {
            margin-top: 20px;
            padding: 20px;
            background: #d4edda;
            border-radius: 10px;
            border: 2px solid #c3e6cb;
            display: none;
        }

        .result-area.show {
            display: block;
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .result-text {
            font-size: 1.5em;
            color: #155724;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍔 随机选择外卖员</h1>

        <div class="display-area">
            <div class="current-name" id="currentName">点击开始选择</div>
        </div>

        <button class="btn" id="startBtn" onclick="startSelection()">开始选择</button>
        <button class="btn" id="resetBtn" onclick="resetSelection()" style="display: none;">重新选择</button>

        <div class="people-list">
            <h3>候选人员名单</h3>
            <div id="peopleContainer"></div>
        </div>

        <div class="result-area" id="resultArea">
            <div class="result-text" id="resultText"></div>
        </div>
    </div>

    <script>
        // 初始化人员数组 - 你可以在这里修改人员名单
        const people = [
            '韩智杰',
            '代一尘',
            '张嘉祺',
            '魏佳奋',
            '束学璋',
            '黄威龙'
        ];

        let isSelecting = false;
        let selectedPerson = '';

        // 初始化页面
        function init() {
            renderPeopleList();
        }

        // 渲染人员列表
        function renderPeopleList() {
            const container = document.getElementById('peopleContainer');
            container.innerHTML = '';

            people.forEach((person, index) => {
                const item = document.createElement('div');
                item.className = 'person-item';
                item.innerHTML = `
                    <span>${person}</span>
                    <span>${index + 1}</span>
                `;
                item.id = `person-${index}`;
                container.appendChild(item);
            });
        }

        // 开始选择
        function startSelection() {
            if (isSelecting) return;

            isSelecting = true;
            const startBtn = document.getElementById('startBtn');
            const currentNameEl = document.getElementById('currentName');
            const resultArea = document.getElementById('resultArea');

            startBtn.disabled = true;
            startBtn.textContent = '选择中...';
            resultArea.classList.remove('show');

            // 清除之前的选中状态
            document.querySelectorAll('.person-item').forEach(item => {
                item.classList.remove('selected-person');
            });

            // 在开始时就确定最终结果，确保真正的随机性
            const finalIndex = Math.floor(Math.random() * people.length);
            selectedPerson = people[finalIndex];

            currentNameEl.classList.add('spinning');

            let counter = 0;
            let currentIndex = 0;
            const totalDuration = 3000; // 总时长3秒
            const fastSpinDuration = 2000; // 快速旋转2秒
            const slowSpinDuration = 1000; // 慢速旋转1秒

            const fastSpinSpeed = 50; // 快速旋转间隔50ms
            const maxFastSpins = Math.floor(fastSpinDuration / fastSpinSpeed);

            // 快速旋转阶段
            const spinInterval = setInterval(() => {
                currentIndex = (currentIndex + 1) % people.length;
                currentNameEl.textContent = people[currentIndex];
                counter++;

                if (counter >= maxFastSpins) {
                    clearInterval(spinInterval);
                    startSlowSpin(currentIndex, finalIndex);
                }
            }, fastSpinSpeed);
        }

        // 慢速旋转阶段
        function startSlowSpin(currentIdx, targetIndex) {
            let currentIndex = currentIdx;
            const slowSpinSteps = 8; // 慢速阶段的步数
            const stepDuration = 1000 / slowSpinSteps; // 每步的时间
            let step = 0;

            const slowSpinInterval = setInterval(() => {
                currentIndex = (currentIndex + 1) % people.length;
                document.getElementById('currentName').textContent = people[currentIndex];
                step++;

                if (step >= slowSpinSteps) {
                    clearInterval(slowSpinInterval);
                    // 确保最后停在目标位置
                    document.getElementById('currentName').textContent = people[targetIndex];
                    finishSelection(targetIndex);
                }
            }, stepDuration + (step * 20)); // 逐渐减速
        }

        // 完成选择
        function finishSelection(finalIndex) {
            const currentNameEl = document.getElementById('currentName');
            const startBtn = document.getElementById('startBtn');
            const resetBtn = document.getElementById('resetBtn');
            const resultArea = document.getElementById('resultArea');
            const resultText = document.getElementById('resultText');

            // 使用预定的结果
            selectedPerson = people[finalIndex];

            currentNameEl.classList.remove('spinning');
            currentNameEl.classList.add('selected');
            currentNameEl.textContent = selectedPerson;

            // 高亮选中的人员
            const selectedItem = document.getElementById(`person-${finalIndex}`);
            selectedItem.classList.add('selected-person');

            // 显示结果
            setTimeout(() => {
                resultText.textContent = `🎉 恭喜 ${selectedPerson}，今天由你去拿外卖！`;
                resultArea.classList.add('show');

                startBtn.style.display = 'none';
                resetBtn.style.display = 'inline-block';
                isSelecting = false;
            }, 1000);
        }

        // 重置选择
        function resetSelection() {
            const currentNameEl = document.getElementById('currentName');
            const startBtn = document.getElementById('startBtn');
            const resetBtn = document.getElementById('resetBtn');
            const resultArea = document.getElementById('resultArea');

            currentNameEl.classList.remove('selected');
            currentNameEl.textContent = '点击开始选择';

            startBtn.disabled = false;
            startBtn.textContent = '开始选择';
            startBtn.style.display = 'inline-block';
            resetBtn.style.display = 'none';

            resultArea.classList.remove('show');

            // 清除选中状态
            document.querySelectorAll('.person-item').forEach(item => {
                item.classList.remove('selected-person');
            });

            selectedPerson = '';
        }

        // 页面加载完成后初始化
        window.addEventListener('load', init);
    </script>
</body>
</html>
